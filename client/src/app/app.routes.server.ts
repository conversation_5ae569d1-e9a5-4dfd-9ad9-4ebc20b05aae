import { RenderMode, ServerRoute } from '@angular/ssr'

export const serverRoutes: ServerRoute[] = [
  // Root redirect - prerender for better performance
  {
    path: '',
    renderMode: RenderMode.Prerender
  },

  // Language-specific main pages - prerender for all supported languages
  {
    path: ':lang',
    renderMode: RenderMode.Prerender,
    async getPrerenderParams() {
      // Support all languages from app config
      return [
        { lang: 'ru' },
        { lang: 'en' },
        { lang: 'de' }
      ];
    }
  },

  // Static pages - prerender for better performance and SEO
  {
    path: ':lang/photo',
    renderMode: RenderMode.Prerender,
    async getPrerenderParams() {
      return [
        { lang: 'ru' },
        { lang: 'en' },
        { lang: 'de' }
      ];
    }
  },

  {
    path: ':lang/audiogallery/audiolektsii',
    renderMode: RenderMode.Prerender,
    async getPrerenderParams() {
      return [
        { lang: 'ru' },
        { lang: 'en' },
        { lang: 'de' }
      ];
    }
  },

  {
    path: ':lang/audiogallery/videolektsii',
    renderMode: RenderMode.Prerender,
    async getPrerenderParams() {
      return [
        { lang: 'ru' },
        { lang: 'en' },
        { lang: 'de' }
      ];
    }
  },

  {
    path: ':lang/library',
    renderMode: RenderMode.Prerender,
    async getPrerenderParams() {
      return [
        { lang: 'ru' },
        { lang: 'en' },
        { lang: 'de' }
      ];
    }
  },

  {
    path: ':lang/categories',
    renderMode: RenderMode.Prerender,
    async getPrerenderParams() {
      return [
        { lang: 'ru' },
        { lang: 'en' },
        { lang: 'de' }
      ];
    }
  },

  {
    path: ':lang/sitemap',
    renderMode: RenderMode.Prerender,
    async getPrerenderParams() {
      return [
        { lang: 'ru' },
        { lang: 'en' },
        { lang: 'de' }
      ];
    }
  },

  {
    path: ':lang/forum',
    renderMode: RenderMode.Prerender,
    async getPrerenderParams() {
      return [
        { lang: 'ru' },
        { lang: 'en' },
        { lang: 'de' }
      ];
    }
  },

  {
    path: ':lang/news',
    renderMode: RenderMode.Prerender,
    async getPrerenderParams() {
      return [
        { lang: 'ru' },
        { lang: 'en' },
        { lang: 'de' }
      ];
    }
  },

  {
    path: ':lang/audiofiles',
    renderMode: RenderMode.Prerender,
    async getPrerenderParams() {
      return [
        { lang: 'ru' },
        { lang: 'en' },
        { lang: 'de' }
      ];
    }
  },

  {
    path: ':lang/search',
    renderMode: RenderMode.Prerender,
    async getPrerenderParams() {
      return [
        { lang: 'ru' },
        { lang: 'en' },
        { lang: 'de' }
      ];
    }
  },

  // User authentication pages - client-side rendering for better UX
  {
    path: ':lang/signin',
    renderMode: RenderMode.Client,
    async getPrerenderParams() {
      return [
        { lang: 'ru' },
        { lang: 'en' },
        { lang: 'de' }
      ];
    }
  },
  {
    path: ':lang/signup',
    renderMode: RenderMode.Client,
    async getPrerenderParams() {
      return [
        { lang: 'ru' },
        { lang: 'en' },
        { lang: 'de' }
      ];
    }
  },
  {
    path: ':lang/forgot',
    renderMode: RenderMode.Client,
    async getPrerenderParams() {
      return [
        { lang: 'ru' },
        { lang: 'en' },
        { lang: 'de' }
      ];
    }
  },

  // User profile pages - client-side rendering (user-specific content)
  {
    path: ':lang/profile/**',
    renderMode: RenderMode.Client,
    async getPrerenderParams() {
      return [
        { lang: 'ru' },
        { lang: 'en' },
        { lang: 'de' }
      ];
    }
  },

  // AI Chat pages - client-side rendering (interactive chat functionality)
  {
    path: ':lang/ai-chat',
    renderMode: RenderMode.Client,
    async getPrerenderParams() {
      return [
        { lang: 'ru' },
        { lang: 'en' },
        { lang: 'de' }
      ];
    }
  },
  {
    path: ':lang/ai-chat/**',
    renderMode: RenderMode.Client,
    async getPrerenderParams() {
      return [
        { lang: 'ru' },
        { lang: 'en' },
        { lang: 'de' }
      ];
    }
  },

  {
    path: ':lang/anketa',
    renderMode: RenderMode.Server,
    async getPrerenderParams() {
      return [
        { lang: 'ru' },
        { lang: 'en' },
        { lang: 'de' }
      ];
    }
  },


   {
    path: ':lang/new-main',
    renderMode: RenderMode.Prerender,
    async getPrerenderParams() {
      return [
        { lang: 'ru' },
        { lang: 'en' },
        { lang: 'de' }
      ];
    }
  },

   {
    path: ':lang/donation',
    renderMode: RenderMode.Prerender,
    async getPrerenderParams() {
      return [
        { lang: 'ru' },
        { lang: 'en' },
        { lang: 'de' }
      ];
    }
  },

   {
    path: ':lang/notifications',
    renderMode: RenderMode.Prerender,
    async getPrerenderParams() {
      return [
        { lang: 'ru' },
        { lang: 'en' },
        { lang: 'de' }
      ];
    }
  },
];
